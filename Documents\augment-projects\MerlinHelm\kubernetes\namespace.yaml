apiVersion: v1
kind: Namespace
metadata:
  name: merlinhelm
  labels:
    name: merlinhelm
    app.kubernetes.io/name: merlinhelm
    app.kubernetes.io/component: namespace
---
apiVersion: v1
kind: Namespace
metadata:
  name: merlinhelm-dev
  labels:
    name: merlinhelm-dev
    app.kubernetes.io/name: merlinhelm
    app.kubernetes.io/component: namespace
    environment: dev
---
apiVersion: v1
kind: Namespace
metadata:
  name: merlinhelm-test
  labels:
    name: merlinhelm-test
    app.kubernetes.io/name: merlinhelm
    app.kubernetes.io/component: namespace
    environment: test
---
apiVersion: v1
kind: Namespace
metadata:
  name: merlinhelm-prod
  labels:
    name: merlinhelm-prod
    app.kubernetes.io/name: merlinhelm
    app.kubernetes.io/component: namespace
    environment: prod
