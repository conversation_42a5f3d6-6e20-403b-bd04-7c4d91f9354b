apiVersion: apps/v1
kind: Deployment
metadata:
  name: merlinhelm-local-ollama
  namespace: merlinhelm-local
  labels:
    app.kubernetes.io/name: ollama
    app.kubernetes.io/instance: merlinhelm-local
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: ollama
      app.kubernetes.io/instance: merlinhelm-local
  template:
    metadata:
      labels:
        app.kubernetes.io/name: ollama
        app.kubernetes.io/instance: merlinhelm-local
    spec:
      serviceAccountName: merlinhelm-local-ollama
      securityContext:
        fsGroup: 2000
      containers:
        - name: ollama
          securityContext:
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: false
            runAsNonRoot: false
            runAsUser: 0
          image: "ollama/ollama:latest"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 11434
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            limits:
              cpu: 2000m
              memory: 4Gi
            requests:
              cpu: 1000m
              memory: 2Gi
          env:
            - name: OLLAMA_HOST
              value: "0.0.0.0"
            - name: OLLAMA_ORIGINS
              value: "*"
          volumeMounts:
            - name: ollama-data
              mountPath: /root/.ollama
      volumes:
        - name: ollama-data
          persistentVolumeClaim:
            claimName: merlinhelm-local-ollama-data
      nodeSelector:
        kubernetes.io/os: linux
