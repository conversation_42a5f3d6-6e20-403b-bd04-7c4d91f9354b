# Default values for ollama
# This is a YAML-formatted file.

replicaCount: 1

image:
  repository: ollama/ollama
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: false
  runAsUser: 0

service:
  type: ClusterIP
  port: 11434
  targetPort: 11434

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: ollama.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  limits:
    memory: 8Gi
    cpu: 2000m
  requests:
    memory: 4Gi
    cpu: 1000m

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  kubernetes.io/os: linux

tolerations: []

affinity: {}

persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 50Gi
  mountPath: /root/.ollama

env:
  - name: OLLAMA_HOST
    value: "0.0.0.0"
  - name: OLLAMA_ORIGINS
    value: "*"

models:
  # List of models to preload
  preload: []
  # Example:
  # preload:
  #   - llama2
  #   - codellama

livenessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5

# GPU configuration
gpu:
  enabled: false
  vendor: nvidia
  count: 1

# Runtime class for GPU support
runtimeClassName: ""
