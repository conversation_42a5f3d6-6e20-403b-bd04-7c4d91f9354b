# MerlinHelm - Kubernetes Deployment for Ollama + OpenWebUI

A comprehensive Kubernetes deployment solution for running Ollama and OpenWebUI with GPU support across dev/test/prod environments.

## 🚀 Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd MerlinHelm

# Deploy to local environment (CPU-only)
./scripts/deploy.sh local

# Deploy to development environment
./scripts/deploy.sh dev

# Deploy to production environment
./scripts/deploy.sh prod
```

## 📁 Repository Structure

```
MerlinHelm/
├── helm/                           # Helm charts
│   ├── ollama/                     # Ollama Helm chart
│   ├── openwebui/                  # OpenWebUI Helm chart
│   └── merlin-stack/               # Combined stack chart
├── environments/                   # Environment-specific configurations
│   ├── local/                      # Local environment (CPU-only)
│   ├── dev/                        # Development environment
│   ├── test/                       # Testing environment
│   └── prod/                       # Production environment
├── kubernetes/                     # Raw Kubernetes manifests
├── .github/workflows/              # CI/CD pipelines
├── scripts/                        # Deployment scripts
└── docs/                          # Documentation
```

## 🛠 Components

- **Ollama**: LLM inference server with GPU support
- **OpenWebUI**: Modern web interface for LLMs
- **Ingress**: NGINX ingress controller
- **Storage**: Persistent volumes for models and data
- **Monitoring**: Prometheus and Grafana (optional)

## 🌍 Environments

### Local
- CPU-only deployments (no GPU required)
- Single replica deployments
- Minimal resource allocation
- Perfect for local development

### Development
- Single replica deployments
- Minimal resource allocation
- Local storage
- Basic monitoring

### Test
- Multi-replica deployments
- Moderate resource allocation
- Persistent storage
- Enhanced monitoring

### Production
- High availability deployments
- Auto-scaling enabled
- Enterprise storage
- Full monitoring stack

## 📋 Prerequisites

- Kubernetes cluster (1.24+)
- Helm 3.x
- kubectl configured
- GPU nodes (for Ollama)
- Ingress controller

## 🚀 Deployment

See [Deployment Guide](docs/deployment.md) for detailed instructions.

## 🧹 Teardown

To completely remove MerlinHelm deployments:

```powershell
# Remove specific environment
.\scripts\teardown.ps1 local

# Remove all environments and infrastructure
.\scripts\teardown.ps1 all --remove-infrastructure --remove-storage

# Dry run to see what would be removed
.\scripts\teardown.ps1 --dry-run
```

## 📖 Documentation

- [Getting Started](GETTING_STARTED.md) - Quick start guide
- [Deployment Summary](DEPLOYMENT_SUMMARY.md) - Complete deployment overview
- [Architecture Overview](docs/architecture.md)
- [Deployment Guide](docs/deployment.md)
- [Configuration Reference](docs/configuration.md)
- [Teardown Guide](docs/teardown.md) - Safe removal procedures
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
