# Test environment values
# Optimized for testing and staging

global:
  environment: test
  domain: test.merlinhelm.local
  storageClass: "fast-ssd"

ollama:
  enabled: true
  replicaCount: 2

  image:
    tag: "latest"
    pullPolicy: IfNotPresent

  resources:
    limits:
      nvidia.com/gpu: 1
      memory: 8Gi
      cpu: 2000m
    requests:
      memory: 4Gi
      cpu: 1000m

  persistence:
    enabled: true
    size: 100Gi
    storageClass: "fast-ssd"

  nodeSelector:
    accelerator: nvidia-tesla-v100

  tolerations:
    - key: nvidia.com/gpu
      operator: Exists
      effect: NoSchedule

  service:
    type: ClusterIP
    port: 11434

  models:
    preload:
      - llama2:7b
      - llama2:13b
      - codellama:7b

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

  # GPU configuration for testing
  gpu:
    enabled: true

openwebui:
  enabled: true
  replicaCount: 2

  image:
    tag: "main"
    pullPolicy: IfNotPresent

  resources:
    limits:
      memory: 2Gi
      cpu: 1000m
    requests:
      memory: 512Mi
      cpu: 250m

  persistence:
    enabled: true
    size: 20Gi
    storageClass: "fast-ssd"

  service:
    type: ClusterIP
    port: 8080

  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-staging"
    hosts:
      - host: openwebui.test.merlinhelm.local
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: openwebui-test-tls
        hosts:
          - openwebui.test.merlinhelm.local

  config:
    ollamaBaseUrl: "http://ollama:11434"
    enableSignup: true
    defaultUserRole: "pending"
    webUIName: "MerlinHelm Test WebUI"
    enableImageGeneration: true
    enableCommunitySharing: false

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

monitoring:
  enabled: true
  prometheus:
    enabled: true
  grafana:
    enabled: true

networkPolicies:
  enabled: true

podDisruptionBudget:
  enabled: true
  minAvailable: 1
