# MerlinHelm Teardown Guide

This guide explains how to safely remove MerlinHelm deployments and infrastructure using the automated teardown scripts.

## 🧹 Teardown Scripts

MerlinHelm provides comprehensive teardown scripts that can remove:
- Helm releases and deployments
- Kubernetes namespaces
- Persistent volumes and data
- Cluster infrastructure (ingress, cert-manager, GPU support)
- Node labels and configurations

### Available Scripts

- **PowerShell**: `scripts/teardown.ps1` (Windows)
- **Bash**: `scripts/teardown.sh` (Linux/macOS)

## 🎯 Quick Start

### Remove Single Environment
```powershell
# Remove local environment only
.\scripts\teardown.ps1 local

# Remove development environment
.\scripts\teardown.ps1 dev
```

### Remove All Environments
```powershell
# Remove all MerlinHelm environments
.\scripts\teardown.ps1 all

# Remove everything including infrastructure
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces
```

### Dry Run (Recommended)
```powershell
# See what would be removed without making changes
.\scripts\teardown.ps1 all -DryRun
```

## 📋 Command Reference

### Environments
- `local` - Remove local environment only
- `dev` - Remove development environment only  
- `test` - Remove test environment only
- `prod` - Remove production environment only
- `all` - Remove all environments (default)

### Options

#### `-RemoveInfrastructure` / `--remove-infrastructure`
Removes cluster infrastructure components:
- NGINX Ingress Controller
- cert-manager and certificates
- NVIDIA Device Plugin (GPU support)
- GPU runtime classes

#### `-RemoveNamespaces` / `--remove-namespaces`
Removes MerlinHelm Kubernetes namespaces:
- `merlinhelm-local`
- `merlinhelm-dev`
- `merlinhelm-test`
- `merlinhelm-prod`

#### `-RemoveStorage` / `--remove-storage`
⚠️ **WARNING: DATA LOSS** - Removes persistent volumes and all stored data:
- Ollama model data
- OpenWebUI user data and conversations
- Configuration data

#### `-Force` / `--force`
Skips all confirmation prompts (use with caution)

#### `-DryRun` / `--dry-run`
Shows what would be removed without making any changes

## 🔄 Teardown Scenarios

### Scenario 1: Clean Development Reset
```powershell
# Remove local environment and start fresh
.\scripts\teardown.ps1 local -RemoveStorage
.\scripts\deploy.ps1 local
```

### Scenario 2: Complete Infrastructure Reset
```powershell
# Remove everything and rebuild from scratch
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces -Force
.\scripts\setup-cluster.ps1 -All
.\scripts\deploy.ps1 local
```

### Scenario 3: Production Environment Removal
```powershell
# Safely remove production (with confirmation)
.\scripts\teardown.ps1 prod -RemoveStorage
```

### Scenario 4: Partial Cleanup
```powershell
# Remove deployments but keep infrastructure
.\scripts\teardown.ps1 all
# Infrastructure (ingress, cert-manager) remains for other projects
```

## ⚠️ Safety Features

### Confirmation Prompts
The teardown script includes multiple confirmation prompts:
- Overall teardown confirmation
- Per-environment confirmation
- Storage removal confirmation (data loss warning)
- Infrastructure removal confirmation

### Dry Run Mode
Always test with `--dry-run` first:
```powershell
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -DryRun
```

### Selective Removal
Remove only what you need:
```powershell
# Remove only deployments (keep data and infrastructure)
.\scripts\teardown.ps1 dev

# Remove deployments and data (keep infrastructure)
.\scripts\teardown.ps1 dev -RemoveStorage

# Remove everything for specific environment
.\scripts\teardown.ps1 dev -RemoveStorage -RemoveNamespaces
```

## 🔍 What Gets Removed

### Helm Releases
- `merlinhelm-local` in `merlinhelm-local` namespace
- `merlinhelm-dev` in `merlinhelm-dev` namespace
- `merlinhelm-test` in `merlinhelm-test` namespace
- `merlinhelm-prod` in `merlinhelm-prod` namespace

### Kubernetes Resources
- Deployments (Ollama, OpenWebUI)
- Services (NodePort/ClusterIP)
- Ingress resources
- ConfigMaps and Secrets
- ServiceAccounts and RBAC

### Persistent Storage (if `-RemoveStorage`)
- Ollama model storage PVCs
- OpenWebUI data storage PVCs
- All associated persistent volumes

### Infrastructure (if `-RemoveInfrastructure`)
- NGINX Ingress Controller
- cert-manager and ClusterIssuers
- NVIDIA Device Plugin
- GPU RuntimeClass

### Node Labels
- `accelerator` labels
- `node-type` labels
- Other MerlinHelm-specific labels

## 🚨 Recovery

### After Accidental Removal
If you accidentally remove too much:

1. **Redeploy infrastructure**:
   ```powershell
   .\scripts\setup-cluster.ps1 -All
   ```

2. **Redeploy applications**:
   ```powershell
   .\scripts\deploy.ps1 local
   ```

3. **Restore data** (if you have backups):
   ```powershell
   # Restore from your backup solution
   ```

### Backup Before Teardown
Always backup important data before teardown:
```powershell
# Backup OpenWebUI data
kubectl cp merlinhelm-local/openwebui-pod:/app/backend/data ./backup/openwebui-data

# Backup Ollama models
kubectl cp merlinhelm-local/ollama-pod:/root/.ollama ./backup/ollama-data
```

## 📝 Examples

### Complete Environment Refresh
```powershell
# 1. Backup data (if needed)
kubectl cp merlinhelm-local/openwebui-pod:/app/backend/data ./backup/

# 2. Complete teardown
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces -Force

# 3. Fresh setup
.\scripts\setup-cluster.ps1 -All
.\scripts\deploy.ps1 local

# 4. Restore data (if needed)
kubectl cp ./backup/ merlinhelm-local/openwebui-pod:/app/backend/data
```

### Selective Environment Management
```powershell
# Remove test environment for maintenance
.\scripts\teardown.ps1 test -RemoveStorage

# Redeploy with updated configuration
.\scripts\deploy.ps1 test

# Production remains untouched
kubectl get pods -n merlinhelm-prod  # Still running
```

## 🔧 Troubleshooting

### Script Fails to Remove Resources
```powershell
# Force removal of stuck resources
kubectl delete namespace merlinhelm-local --force --grace-period=0

# Remove finalizers if needed
kubectl patch pvc pvc-name -p '{"metadata":{"finalizers":null}}'
```

### Infrastructure Won't Remove
```powershell
# Manually remove stuck infrastructure
helm uninstall ingress-nginx -n ingress-nginx
helm uninstall cert-manager -n cert-manager
kubectl delete namespace ingress-nginx cert-manager --force
```

### Persistent Volumes Stuck
```powershell
# Check PV status
kubectl get pv

# Force delete if needed
kubectl delete pv pv-name --force --grace-period=0
```

## ✅ Verification

After teardown, verify removal:
```powershell
# Check namespaces
kubectl get namespaces | grep merlinhelm

# Check Helm releases
helm list --all-namespaces | grep merlinhelm

# Check persistent volumes
kubectl get pv | grep merlinhelm

# Check infrastructure
kubectl get pods -n ingress-nginx
kubectl get pods -n cert-manager
```

## 🎯 Best Practices

1. **Always use dry-run first**
2. **Backup important data before teardown**
3. **Use selective removal when possible**
4. **Verify removal after teardown**
5. **Document what you're removing and why**
6. **Test teardown scripts in development first**

The teardown scripts provide a safe, comprehensive way to remove MerlinHelm deployments while giving you full control over what gets removed.
