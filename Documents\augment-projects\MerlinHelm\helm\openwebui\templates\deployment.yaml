apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "openwebui.fullname" . }}
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "openwebui.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "openwebui.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "openwebui.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: OLLAMA_BASE_URL
              value: "http://ollama:11434"
            - name: WEBUI_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.fullname" . }}-secret
                  key: secret-key
            - name: ENABLE_SIGNUP
              value: "true"
            - name: DEFAULT_USER_ROLE
              value: "pending"
            {{- if .Values.env }}
            {{- range .Values.env }}
            {{- if ne .name "WEBUI_SECRET_KEY" }}
            {{- if ne .name "OLLAMA_BASE_URL" }}
            {{- if ne .name "ENABLE_SIGNUP" }}
            {{- if ne .name "DEFAULT_USER_ROLE" }}
            - {{- toYaml . | nindent 14 }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
          {{- if .Values.persistence.enabled }}
          volumeMounts:
            - name: openwebui-data
              mountPath: {{ .Values.persistence.mountPath }}
          {{- end }}
      {{- if .Values.persistence.enabled }}
      volumes:
        - name: openwebui-data
          persistentVolumeClaim:
            claimName: {{ include "openwebui.fullname" . }}-data
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
